import additionalServiceApi from '../../../api/modules/additionalService.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,
    orderDetail: null,
    
    // 可用服务列表
    availableServices: [],
    selectedServices: [], // 已选择的服务 [{serviceId, quantity, service}]
    
    // 优惠信息
    availableCards: [],
    availableCoupons: [],
    selectedCardId: '',
    selectedCouponId: '',
    
    // 价格计算
    originalPrice: 0,
    totalFee: 0,
    cardDeduction: 0,
    couponDeduction: 0,
    
    // 备注
    remark: '',
    
    // UI状态
    loading: false,
    showDiscountSelector: false,
    
    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: []
  },

  onLoad(options) {
    const { orderDetailId } = options;
    const userInfo = Session.getUser();
    
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId)
    });

    this.loadData();
  },

  /**
   * 加载页面数据
   */
  async loadData() {
    try {
      wx.showLoading({ title: '加载中...' });
      
      // 并行加载可用服务和优惠信息
      await Promise.all([
        this.loadAvailableServices(),
        this.loadAvailableDiscounts()
      ]);
      
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 加载可用服务
   */
  async loadAvailableServices() {
    try {
      const services = await additionalServiceApi.getAvailableServices(this.data.orderDetailId);
      this.setData({
        availableServices: services || []
      });
    } catch (error) {
      console.error('加载可用服务失败:', error);
      throw error;
    }
  },

  /**
   * 加载可用优惠信息
   */
  async loadAvailableDiscounts() {
    try {
      const { userInfo } = this.data;
      
      const [cards, coupons] = await Promise.all([
        additionalServiceApi.getAvailableCards(userInfo.id),
        additionalServiceApi.getAvailableCoupons(userInfo.id)
      ]);
      
      this.setData({
        availableCards: cards || [],
        availableCoupons: coupons || []
      });
    } catch (error) {
      console.error('加载优惠信息失败:', error);
      throw error;
    }
  },

  /**
   * 选择服务
   */
  selectService(e) {
    const { service } = e.currentTarget.dataset;
    const { selectedServices } = this.data;
    
    // 检查是否已选择
    const existingIndex = selectedServices.findIndex(item => item.serviceId === service.id);
    
    if (existingIndex >= 0) {
      // 已选择，增加数量
      const updatedServices = [...selectedServices];
      updatedServices[existingIndex].quantity += 1;
      this.setData({ selectedServices: updatedServices });
    } else {
      // 未选择，添加到列表
      const newService = {
        serviceId: service.id,
        quantity: 1,
        service: service
      };
      this.setData({
        selectedServices: [...selectedServices, newService]
      });
    }
    
    this.calculatePrice();
  },

  /**
   * 调整服务数量
   */
  adjustServiceQuantity(e) {
    const { serviceId, action } = e.currentTarget.dataset;
    const { selectedServices } = this.data;
    
    const updatedServices = selectedServices.map(item => {
      if (item.serviceId === serviceId) {
        const newQuantity = action === 'increase' ? item.quantity + 1 : item.quantity - 1;
        return { ...item, quantity: Math.max(0, newQuantity) };
      }
      return item;
    }).filter(item => item.quantity > 0); // 移除数量为0的服务
    
    this.setData({ selectedServices: updatedServices });
    this.calculatePrice();
  },

  /**
   * 计算价格
   */
  calculatePrice() {
    const { selectedServices, selectedCardId, selectedCouponId, availableCards, availableCoupons } = this.data;
    
    // 计算原价
    const originalPrice = selectedServices.reduce((total, item) => {
      return total + (item.service.price * item.quantity);
    }, 0);
    
    // 计算权益卡抵扣
    let cardDeduction = 0;
    if (selectedCardId) {
      const selectedCard = availableCards.find(card => card.id === selectedCardId);
      if (selectedCard && selectedCard.type === 'discount') {
        cardDeduction = originalPrice * (selectedCard.discountRate / 100);
      }
    }
    
    // 计算优惠券抵扣
    let couponDeduction = 0;
    if (selectedCouponId) {
      const selectedCoupon = availableCoupons.find(coupon => coupon.id === selectedCouponId);
      if (selectedCoupon) {
        couponDeduction = Math.min(selectedCoupon.amount, originalPrice - cardDeduction);
      }
    }
    
    const totalFee = Math.max(0, originalPrice - cardDeduction - couponDeduction);
    
    this.setData({
      originalPrice: originalPrice.toFixed(2),
      cardDeduction: cardDeduction.toFixed(2),
      couponDeduction: couponDeduction.toFixed(2),
      totalFee: totalFee.toFixed(2)
    });
  },

  /**
   * 显示优惠选择器
   */
  showDiscountSelector() {
    this.setData({ showDiscountSelector: true });
  },

  /**
   * 隐藏优惠选择器
   */
  hideDiscountSelector() {
    this.setData({ showDiscountSelector: false });
  },

  /**
   * 选择权益卡
   */
  selectCard(e) {
    const { cardId } = e.currentTarget.dataset;
    const { selectedCardId } = this.data;
    
    this.setData({
      selectedCardId: selectedCardId === cardId ? '' : cardId,
      selectedCouponId: '' // 清除优惠券选择
    });
    
    this.calculatePrice();
  },

  /**
   * 选择优惠券
   */
  selectCoupon(e) {
    const { couponId } = e.currentTarget.dataset;
    const { selectedCouponId } = this.data;
    
    this.setData({
      selectedCouponId: selectedCouponId === couponId ? '' : couponId,
      selectedCardId: '' // 清除权益卡选择
    });
    
    this.calculatePrice();
  },

  /**
   * 备注输入
   */
  onRemarkInput(e) {
    this.setData({ remark: e.detail.value });
  },

  /**
   * 提交申请
   */
  async submitApplication() {
    const { selectedServices, userInfo, orderDetailId, selectedCardId, selectedCouponId, remark, availableCards, availableCoupons } = this.data;
    
    if (selectedServices.length === 0) {
      wx.showToast({
        title: '请选择要追加的服务',
        icon: 'none'
      });
      return;
    }
    
    try {
      wx.showLoading({ title: '提交中...' });
      
      // 构建请求数据
      const requestData = {
        customerId: userInfo.id,
        services: selectedServices.map(item => ({
          serviceId: item.serviceId,
          quantity: item.quantity
        })),
        remark
      };
      
      // 添加优惠信息
      const discountInfos = [];
      if (selectedCardId) {
        const selectedCard = availableCards.find(card => card.id === selectedCardId);
        if (selectedCard) {
          discountInfos.push({
            discountType: 'membership_card',
            discountId: selectedCardId,
            discountAmount: parseFloat(this.data.cardDeduction)
          });
        }
      }
      
      if (selectedCouponId) {
        const selectedCoupon = availableCoupons.find(coupon => coupon.id === selectedCouponId);
        if (selectedCoupon) {
          discountInfos.push({
            discountType: 'coupon',
            discountId: selectedCouponId,
            discountAmount: parseFloat(this.data.couponDeduction)
          });
        }
      }
      
      if (discountInfos.length > 0) {
        requestData.discountInfos = discountInfos;
      }
      
      // 提交申请
      const result = await additionalServiceApi.create(orderDetailId, requestData);
      
      wx.hideLoading();
      
      // 显示成功提示
      this.setData({
        showModal: true,
        modalTitle: '申请成功',
        modalContent: '您的追加服务申请已提交，请等待员工确认',
        modalButtons: [
          {
            text: '确定',
            type: 'primary',
            event: 'handleModalConfirm'
          }
        ]
      });
      
    } catch (error) {
      wx.hideLoading();
      console.error('提交申请失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 模态框确认
   */
  handleModalConfirm() {
    this.setData({ showModal: false });
    wx.navigateBack();
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  }
});
