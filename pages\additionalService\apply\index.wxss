.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.service-item:active {
  background-color: #f0f0f0;
  border-color: #007aff;
}

.service-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.service-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
}

.service-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

.service-action {
  display: flex;
  align-items: center;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

/* 已选服务样式 */
.selected-services {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.selected-service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

.quantity {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  min-width: 40rpx;
  text-align: center;
}

/* 优惠选择样式 */
.discount-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
}

.discount-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.discount-label {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.discount-desc {
  font-size: 24rpx;
  color: #666;
}

.arrow {
  font-size: 32rpx;
  color: #999;
}

/* 备注输入样式 */
.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
}

/* 价格汇总样式 */
.price-summary {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.price-row:not(:last-child) {
  border-bottom: 1rpx solid #f0f0f0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.price-row.discount .price-value {
  color: #ff6b35;
}

.price-row.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #e5e5e5;
}

.price-row.total .price-label {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 提交按钮样式 */
.submit-section {
  padding: 20rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:disabled {
  background-color: #ccc;
}

/* 优惠选择弹窗样式 */
.discount-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.discount-modal-content {
  width: 100%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
}

.discount-category {
  margin-bottom: 30rpx;
}

.category-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.discount-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.discount-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.discount-item.selected {
  border-color: #007aff;
  background-color: #f0f8ff;
}

.discount-item-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.discount-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.selected-icon {
  color: #007aff;
  font-size: 32rpx;
  font-weight: bold;
}

.modal-actions {
  padding-top: 30rpx;
  border-top: 1rpx solid #e5e5e5;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.confirm-btn {
  background-color: #007aff;
  color: #fff;
}
